import React, { useState, useCallback, useEffect } from 'react';
import { Type } from 'lucide-react';

/**
 * Font Size Editor Component
 * Provides slider and input controls for adjusting font size
 * with real-time preview and mobile-optimized interface
 */
const FontSizeEditor = ({
  value = 16,
  onChange = null,
  min = 8,
  max = 72,
  step = 1,
  isMobile = false,
  className = ''
}) => {
  const [inputValue, setInputValue] = useState(value.toString());

  // Update input when value prop changes
  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  // Handle slider change
  const handleSliderChange = useCallback((event) => {
    const newValue = parseInt(event.target.value, 10);
    setInputValue(newValue.toString());
    onChange?.(newValue);
  }, [onChange]);

  // Handle input change
  const handleInputChange = useCallback((event) => {
    const newValue = event.target.value;
    setInputValue(newValue);

    // Validate and apply if valid
    const numValue = parseInt(newValue, 10);
    if (!isNaN(numValue) && numValue >= min && numValue <= max) {
      onChange?.(numValue);
    }
  }, [onChange, min, max]);

  // Handle input blur (apply value even if slightly out of range)
  const handleInputBlur = useCallback(() => {
    const numValue = parseInt(inputValue, 10);
    if (isNaN(numValue)) {
      setInputValue(value.toString());
      return;
    }

    // Clamp to valid range
    const clampedValue = Math.max(min, Math.min(max, numValue));
    setInputValue(clampedValue.toString());
    
    if (clampedValue !== value) {
      onChange?.(clampedValue);
    }
  }, [inputValue, value, onChange, min, max]);

  // Quick size presets
  const presets = [
    { label: 'Small', value: 14 },
    { label: 'Medium', value: 24 },
    { label: 'Large', value: 36 },
    { label: 'XL', value: 48 }
  ];

  const handlePresetClick = useCallback((presetValue) => {
    setInputValue(presetValue.toString());
    onChange?.(presetValue);
  }, [onChange]);

  return (
    <div className={`font-size-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <Type className="w-4 h-4 text-gray-600" />
        <label className="block text-sm font-medium text-gray-700">
          Font Size
        </label>
        <span className="text-xs text-gray-500">({value}px)</span>
      </div>

      {/* Slider */}
      <div className="mb-4">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={handleSliderChange}
          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider ${
            isMobile ? 'h-3' : 'h-2'
          }`}
          style={{
            background: `linear-gradient(to right, #3B82F6 0%, #3B82F6 ${
              ((value - min) / (max - min)) * 100
            }%, #E5E7EB ${((value - min) / (max - min)) * 100}%, #E5E7EB 100%)`
          }}
        />
        
        {/* Slider labels */}
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{min}px</span>
          <span>{max}px</span>
        </div>
      </div>

      {/* Input and Presets */}
      <div className="flex items-center space-x-2 mb-3">
        {/* Direct input */}
        <div className="flex-1">
          <div className="relative">
            <input
              type="number"
              min={min}
              max={max}
              value={inputValue}
              onChange={handleInputChange}
              onBlur={handleInputBlur}
              className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                isMobile ? 'text-base' : 'text-sm'
              }`}
              placeholder="Size"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
              px
            </span>
          </div>
        </div>

        {/* Increment/Decrement buttons */}
        <div className="flex flex-col">
          <button
            onClick={() => {
              const newValue = Math.min(max, value + step);
              setInputValue(newValue.toString());
              onChange?.(newValue);
            }}
            className={`px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 border border-gray-300 rounded-t hover:bg-gray-200 transition-colors ${
              isMobile ? 'px-3 py-2' : 'px-2 py-1'
            }`}
            disabled={value >= max}
          >
            +
          </button>
          <button
            onClick={() => {
              const newValue = Math.max(min, value - step);
              setInputValue(newValue.toString());
              onChange?.(newValue);
            }}
            className={`px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 border border-gray-300 rounded-b border-t-0 hover:bg-gray-200 transition-colors ${
              isMobile ? 'px-3 py-2' : 'px-2 py-1'
            }`}
            disabled={value <= min}
          >
            -
          </button>
        </div>
      </div>
    </div>
  );
};

export default FontSizeEditor;
