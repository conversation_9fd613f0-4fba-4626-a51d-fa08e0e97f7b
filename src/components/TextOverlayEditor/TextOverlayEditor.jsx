import React, { useState, useCallback, useEffect } from 'react';
import { ChevronLeft, ChevronRight, RotateCcw, Eye, EyeOff } from 'lucide-react';
import FontSizeEditor from './PropertyEditors/FontSizeEditor';
import ColorEditor from './PropertyEditors/ColorEditor';
import PositionEditor from './PropertyEditors/PositionEditor';
import AlignmentEditor from './PropertyEditors/AlignmentEditor';
import StyleEditor from './PropertyEditors/StyleEditor';

/**
 * Text Overlay Editor Component
 * Provides a side panel interface for editing text overlay properties
 * with real-time preview updates and mobile-responsive design
 */
const TextOverlayEditor = ({
  template = null,
  customizations = {},
  onCustomizationChange = null,
  onReset = null,
  isVisible = true,
  onToggleVisibility = null,
  className = ''
}) => {
  const [selectedOverlayId, setSelectedOverlayId] = useState(null);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Get overlays from template
  const overlays = template?.text_overlays?.overlays || [];

  // Select first overlay by default
  useEffect(() => {
    if (overlays.length > 0 && !selectedOverlayId) {
      setSelectedOverlayId(overlays[0].id);
    }
  }, [overlays, selectedOverlayId]);

  // Get current overlay data
  const selectedOverlay = overlays.find(overlay => overlay.id === selectedOverlayId);
  const overlayCustomizations = customizations[selectedOverlayId] || {};

  // Merge original styling with customizations
  const currentStyling = selectedOverlay ? {
    ...selectedOverlay.styling,
    ...overlayCustomizations.styling
  } : {};

  const currentPosition = selectedOverlay ? {
    ...selectedOverlay.position,
    ...overlayCustomizations.position
  } : {};

  // Handle property changes
  const handlePropertyChange = useCallback((property, value) => {
    if (!selectedOverlayId || !onCustomizationChange) return;

    const updatedCustomizations = {
      ...customizations,
      [selectedOverlayId]: {
        ...customizations[selectedOverlayId],
        [property]: {
          ...customizations[selectedOverlayId]?.[property],
          ...value
        }
      }
    };

    onCustomizationChange(updatedCustomizations);
  }, [selectedOverlayId, customizations, onCustomizationChange]);

  // Handle styling changes
  const handleStylingChange = useCallback((styleProperty, value) => {
    handlePropertyChange('styling', { [styleProperty]: value });
  }, [handlePropertyChange]);

  // Handle position changes
  const handlePositionChange = useCallback((positionProperty, value) => {
    handlePropertyChange('position', { [positionProperty]: value });
  }, [handlePropertyChange]);

  // Reset overlay to defaults
  const handleResetOverlay = useCallback(() => {
    if (!selectedOverlayId || !onCustomizationChange) return;

    const updatedCustomizations = { ...customizations };
    delete updatedCustomizations[selectedOverlayId];
    onCustomizationChange(updatedCustomizations);
  }, [selectedOverlayId, customizations, onCustomizationChange]);

  // Toggle panel visibility
  const handleToggleCollapse = useCallback(() => {
    setIsCollapsed(!isCollapsed);
  }, [isCollapsed]);

  if (!isVisible) return null;

  return (
    <div className={`text-overlay-editor ${className}`}>
      {/* Mobile: Full-screen overlay */}
      <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-50">
        <div className="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl max-h-[80vh] overflow-hidden">
          <MobileEditorContent
            overlays={overlays}
            selectedOverlayId={selectedOverlayId}
            onSelectOverlay={setSelectedOverlayId}
            currentStyling={currentStyling}
            currentPosition={currentPosition}
            onStylingChange={handleStylingChange}
            onPositionChange={handlePositionChange}
            onReset={handleResetOverlay}
            onClose={() => onToggleVisibility?.(false)}
          />
        </div>
      </div>

      {/* Desktop: Side panel */}
      <div className="hidden lg:block">
        <DesktopEditorPanel
          isCollapsed={isCollapsed}
          onToggleCollapse={handleToggleCollapse}
          overlays={overlays}
          selectedOverlayId={selectedOverlayId}
          onSelectOverlay={setSelectedOverlayId}
          currentStyling={currentStyling}
          currentPosition={currentPosition}
          onStylingChange={handleStylingChange}
          onPositionChange={handlePositionChange}
          onReset={handleResetOverlay}
          onResetAll={onReset}
        />
      </div>
    </div>
  );
};

/**
 * Mobile Editor Content Component
 */
const MobileEditorContent = ({
  overlays,
  selectedOverlayId,
  onSelectOverlay,
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onReset,
  onClose
}) => {
  return (
    <div className="flex flex-col h-full">
      {/* Mobile Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Edit Text Style</h3>
        <button
          onClick={onClose}
          className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
        >
          <ChevronLeft className="w-5 h-5" />
        </button>
      </div>

      {/* Overlay Selection */}
      <div className="p-4 border-b border-gray-200">
        <OverlaySelector
          overlays={overlays}
          selectedOverlayId={selectedOverlayId}
          onSelectOverlay={onSelectOverlay}
          isMobile={true}
        />
      </div>

      {/* Editor Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <EditorContent
          currentStyling={currentStyling}
          currentPosition={currentPosition}
          onStylingChange={onStylingChange}
          onPositionChange={onPositionChange}
          onReset={onReset}
          isMobile={true}
        />
      </div>
    </div>
  );
};

/**
 * Desktop Editor Panel Component
 */
const DesktopEditorPanel = ({
  isCollapsed,
  onToggleCollapse,
  overlays,
  selectedOverlayId,
  onSelectOverlay,
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onReset,
  onResetAll
}) => {
  const panelWidth = isCollapsed ? '48px' : '320px';

  return (
    <div
      className="fixed right-0 top-0 h-full bg-white border-l border-gray-200 shadow-lg transition-all duration-300 z-40"
      style={{ width: panelWidth }}
    >
      {/* Collapse Toggle */}
      <button
        onClick={onToggleCollapse}
        className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full bg-white border border-gray-200 rounded-l-lg p-2 shadow-md hover:bg-gray-50 transition-colors"
      >
        {isCollapsed ? (
          <ChevronLeft className="w-4 h-4 text-gray-600" />
        ) : (
          <ChevronRight className="w-4 h-4 text-gray-600" />
        )}
      </button>

      {!isCollapsed && (
        <div className="flex flex-col h-full">
          {/* Panel Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Edit Text Style</h3>
              <button
                onClick={onResetAll}
                className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                title="Reset all changes"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
            </div>

            {/* Overlay Selection */}
            <OverlaySelector
              overlays={overlays}
              selectedOverlayId={selectedOverlayId}
              onSelectOverlay={onSelectOverlay}
              isMobile={false}
            />
          </div>

          {/* Editor Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <EditorContent
              currentStyling={currentStyling}
              currentPosition={currentPosition}
              onStylingChange={onStylingChange}
              onPositionChange={onPositionChange}
              onReset={onReset}
              isMobile={false}
            />
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Overlay Selector Component
 */
const OverlaySelector = ({ overlays, selectedOverlayId, onSelectOverlay, isMobile }) => {
  if (overlays.length === 0) return null;

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        Select Text Element
      </label>
      <div className={`grid gap-2 ${isMobile ? 'grid-cols-1' : 'grid-cols-1'}`}>
        {overlays.map((overlay) => (
          <button
            key={overlay.id}
            onClick={() => onSelectOverlay(overlay.id)}
            className={`p-3 text-left rounded-lg border transition-colors ${
              selectedOverlayId === overlay.id
                ? 'border-blue-500 bg-blue-50 text-blue-900'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="font-medium capitalize">{overlay.id}</div>
            <div className="text-sm text-gray-500 truncate">
              {overlay.placeholder}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

/**
 * Editor Content Component
 */
const EditorContent = ({
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onReset,
  isMobile
}) => {
  return (
    <div className="space-y-6">
      {/* Font Size */}
      <FontSizeEditor
        value={currentStyling.fontSize || 16}
        onChange={(value) => onStylingChange('fontSize', value)}
        isMobile={isMobile}
      />

      {/* Color */}
      <ColorEditor
        value={currentStyling.color || '#000000'}
        onChange={(value) => onStylingChange('color', value)}
        isMobile={isMobile}
      />

      {/* Text Alignment */}
      <AlignmentEditor
        value={currentStyling.textAlign || 'left'}
        onChange={(value) => onStylingChange('textAlign', value)}
        isMobile={isMobile}
      />

      {/* Font Style */}
      <StyleEditor
        fontWeight={currentStyling.fontWeight || 'normal'}
        fontStyle={currentStyling.fontStyle || 'normal'}
        onFontWeightChange={(value) => onStylingChange('fontWeight', value)}
        onFontStyleChange={(value) => onStylingChange('fontStyle', value)}
        isMobile={isMobile}
      />

      {/* Position */}
      <PositionEditor
        position={currentPosition}
        onChange={onPositionChange}
        isMobile={isMobile}
      />

      {/* Reset Button */}
      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={onReset}
          className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Reset to Default
        </button>
      </div>
    </div>
  );
};

export default TextOverlayEditor;
