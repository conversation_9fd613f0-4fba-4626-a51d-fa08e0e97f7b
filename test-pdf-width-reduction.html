<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Width Reduction Test</title>
    <style>
        /* Test the new PDF width reduction implementation */
        .title-page {
            text-align: center;
            page-break-after: always;
            width: 100%;
            height: 100vh;
            margin: 0;
            padding: 0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
        }
        .author {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        .description {
            font-style: italic;
            color: #95a5a6;
            max-width: 600px;
            margin: 0 auto;
        }
        .chapter {
            margin-bottom: 40px;
            page-break-before: always;
        }
        .chapter-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
            letter-spacing: -0.02em;
        }
        .chapter-content {
            font-size: 16px;
            line-height: 1.6;
            color: #374151;
        }
        h1, h2, h3 {
            color: #000000;
            letter-spacing: -0.02em;
        }
        
        @media print {
            /* FULL-BLEED: Different page setup for cover vs content pages */
            @page {
                size: A4;
                margin: 0; /* No margin for full-bleed cover */
                /* Remove browser-generated headers and footers */
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }

            /* FULL-BLEED: Separate page setup for content pages */
            @page content {
                size: A4;
                margin: 0.5in; /* Reduced margins for better space utilization */
            }

            /* FIXED: Remove conflicting margin/padding that causes blank pages */
            html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0 !important;
                padding: 0 !important;
                height: auto !important;
                background: white !important;
            }

            body {
                font-size: 14pt; /* Increased from 12pt for better readability */
                line-height: 1.5;
                max-width: none !important; /* Allow full page width for container positioning */
                margin: 0 !important; /* Remove auto margins - centering handled by content containers */
                padding: 0 !important; /* Remove padding - margins handled by @page rule */
                
                /* Ensure proper text rendering for narrower content */
                text-align: left; /* Maintain left alignment for readability */
                word-wrap: break-word; /* Handle long words gracefully */
                overflow-wrap: break-word; /* Modern word wrapping */
            }

            /* Content pages use optimized margins for better space utilization */
            .chapter-content {
                page: content;
                margin: 0; /* Remove redundant margin - page margins are handled by @page content rule */
                width: 100%; /* Full container width */
                
                /* READABILITY IMPROVEMENT: Constrain content width for better readability */
                max-width: 5.5in; /* Optimal reading width (~65% of A4 content area) */
                margin-left: auto;
                margin-right: auto;
                padding: 0 0.25in; /* Small padding for content breathing room */
            }

            .chapter {
                page-break-before: always;
                width: 100%; /* Full container width */
                
                /* READABILITY IMPROVEMENT: Constrain content width for better readability */
                max-width: 5.5in; /* Optimal reading width (~65% of A4 content area) */
                margin-left: auto;
                margin-right: auto;
                padding: 0 0.25in; /* Small padding for content breathing room */
            }

            /* Constrain text elements for optimal readability while allowing full width for special elements */
            p, h1, h2, h3, h4, h5, h6, ul, ol, blockquote {
                width: 100%;
                box-sizing: border-box;
                /* Text elements inherit the container's max-width constraint */
            }
            
            /* Allow tables and images to use more space if needed */
            table, img, pre, code {
                width: 100%;
                box-sizing: border-box;
                max-width: 100%; /* Can expand to full container width */
            }
            
            /* Enhanced image handling for narrower content */
            .chapter-image, img {
                page-break-inside: avoid;
                max-width: 100%;
                height: auto;
                margin: 0.5rem auto; /* Center images with some spacing */
                display: block;
            }
            
            /* Improve table presentation in narrower layout */
            table {
                margin: 0.5rem auto;
                border-collapse: collapse;
                font-size: 0.9em; /* Slightly smaller for better fit */
            }
            
            /* Code blocks with better formatting for narrow content */
            pre, code {
                font-size: 0.85em;
                overflow-wrap: break-word;
                word-wrap: break-word;
            }
        }
    </style>
</head>
<body>
    <div class="title-page">
        <div>
            <h1 class="title">PDF Width Reduction Test</h1>
            <div class="author">by DocForge AI</div>
            <div class="description">Testing the new content width constraints for better PDF readability</div>
        </div>
    </div>
    
    <div class="chapter-content">
        <div class="chapter">
            <h1 class="chapter-title">Chapter 1: Testing Content Width Reduction</h1>
            <p>This document tests the new PDF width reduction implementation. The content should now be constrained to approximately 5.5 inches width (about 65% of the A4 content area) for optimal readability. This line should demonstrate the improved line length that makes reading more comfortable and follows typography best practices.</p>
            
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            
            <h2>Benefits of Narrower Content Width</h2>
            <ul>
                <li>Improved readability with optimal line length (45-75 characters)</li>
                <li>Better eye tracking and reduced fatigue</li>
                <li>Professional document appearance</li>
                <li>Follows typography best practices</li>
            </ul>
            
            <h3>Technical Implementation</h3>
            <p>The implementation uses CSS max-width constraints applied only to print media queries, ensuring that:</p>
            <ol>
                <li>Web preview remains unchanged</li>
                <li>PDF exports have narrower, more readable content</li>
                <li>Images and tables can still use full container width when needed</li>
                <li>Content is properly centered on the page</li>
            </ol>
        </div>
        
        <div class="chapter">
            <h1 class="chapter-title">Chapter 2: Content Examples</h1>
            <p>This chapter demonstrates how different content types appear with the new width constraints.</p>
            
            <h2>Code Example</h2>
            <pre><code>function testPdfWidth() {
    const maxWidth = '5.5in';
    const margin = 'auto';
    return 'Improved readability!';
}</code></pre>
            
            <h2>Table Example</h2>
            <table border="1" style="width: 100%; border-collapse: collapse;">
                <tr>
                    <th style="padding: 8px; border: 1px solid #ccc;">Feature</th>
                    <th style="padding: 8px; border: 1px solid #ccc;">Before</th>
                    <th style="padding: 8px; border: 1px solid #ccc;">After</th>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ccc;">Content Width</td>
                    <td style="padding: 8px; border: 1px solid #ccc;">Full page width</td>
                    <td style="padding: 8px; border: 1px solid #ccc;">5.5 inches max</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ccc;">Readability</td>
                    <td style="padding: 8px; border: 1px solid #ccc;">Lines too long</td>
                    <td style="padding: 8px; border: 1px solid #ccc;">Optimal line length</td>
                </tr>
            </table>
            
            <blockquote style="border-left: 4px solid #ccc; padding-left: 1rem; margin: 1rem 0; font-style: italic;">
                "The measure of a line of type is usually about 1.5 to 2 times the length of the lowercase alphabet of the type face, or 45 to 75 characters including spaces." - Typography best practices
            </blockquote>
        </div>
    </div>
</body>
</html>
